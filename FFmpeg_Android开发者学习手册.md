# FFmpeg Android开发者学习手册

## 目录
1. [FFmpeg简介](#ffmpeg简介)
2. [FFmpeg核心概念](#ffmpeg核心概念)
3. [Android中的FFmpeg](#android中的ffmpeg)
4. [基础命令详解](#基础命令详解)
5. [常用音视频处理场景](#常用音视频处理场景)
6. [Android集成实践](#android集成实践)
7. [性能优化建议](#性能优化建议)
8. [常见问题解答](#常见问题解答)

---

## FFmpeg简介

### 什么是FFmpeg？
FFmpeg是一个完整的、跨平台的音视频处理解决方案，能够**解码**、**编码**、**转码**、**复用**、**解复用**、**流式传输**、**过滤**和**播放**几乎所有人类和机器创建的多媒体格式。

### FFmpeg的核心优势
- **格式支持广泛**：支持从古老格式到最新标准的所有音视频格式
- **高度可移植**：在Linux、macOS、Windows、Android、iOS等平台运行
- **性能优异**：高度优化的代码，支持硬件加速
- **开源免费**：完全开源，无需授权费用
- **功能强大**：从简单格式转换到复杂的音视频处理

### FFmpeg工具集
FFmpeg项目包含以下核心工具：

#### 1. ffmpeg命令行工具
- **功能**：多媒体文件格式转换的命令行工具
- **用途**：转码、格式转换、基础编辑
- **示例**：`ffmpeg -i input.mp4 output.avi`

#### 2. ffplay媒体播放器
- **功能**：基于SDL和FFmpeg库的简单媒体播放器
- **用途**：测试、预览、调试音视频文件
- **示例**：`ffplay video.mp4`

#### 3. ffprobe分析工具
- **功能**：多媒体流分析器
- **用途**：获取音视频文件的详细信息
- **示例**：`ffprobe -v quiet -print_format json -show_format input.mp4`

---

## FFmpeg核心概念

### 1. 容器格式 (Container Format)
容器格式是存储音视频数据的文件格式，如MP4、AVI、MKV等。

```bash
# 查看支持的容器格式
ffmpeg -formats
```

### 2. 编解码器 (Codec)
编解码器负责压缩和解压缩音视频数据。

**常见视频编解码器：**
- H.264/AVC：最广泛使用的视频编码标准
- H.265/HEVC：新一代高效视频编码
- VP8/VP9：Google开发的开源编码器
- AV1：最新的开源视频编码标准

**常见音频编解码器：**
- AAC：高质量音频编码，移动设备首选
- MP3：经典音频格式，兼容性好
- Opus：现代低延迟音频编码
- PCM：无损音频格式

```bash
# 查看支持的编解码器
ffmpeg -codecs
```

### 3. 流 (Stream)
一个媒体文件可以包含多个流：
- **视频流**：图像数据
- **音频流**：声音数据  
- **字幕流**：文本或图像字幕
- **数据流**：元数据或其他信息

### 4. 比特率 (Bitrate)
比特率决定音视频质量和文件大小：
- **CBR (Constant Bitrate)**：恒定比特率
- **VBR (Variable Bitrate)**：可变比特率
- **ABR (Average Bitrate)**：平均比特率

### 5. 分辨率和帧率
- **分辨率**：视频的像素尺寸（如1920x1080）
- **帧率**：每秒显示的帧数（如30fps、60fps）

---

## Android中的FFmpeg

### Android平台特点
1. **ARM架构**：大多数Android设备使用ARM处理器
2. **硬件加速**：支持MediaCodec硬件编解码
3. **存储限制**：移动设备存储空间有限
4. **电池续航**：需要考虑功耗优化
5. **网络环境**：移动网络带宽限制

### FFmpeg在Android中的应用场景
1. **视频播放器**：解码各种格式的视频文件
2. **短视频应用**：视频录制、编辑、特效处理
3. **直播应用**：实时音视频编码和流媒体传输
4. **音频处理**：音频录制、格式转换、音效处理
5. **图像处理**：视频帧提取、缩略图生成

### Android集成方式
1. **预编译库**：使用已编译的.so文件
2. **源码编译**：从源码编译适合Android的版本
3. **第三方封装**：使用封装好的Android库

---

## 基础命令详解

### 命令基本语法
```bash
ffmpeg [全局选项] {[输入文件选项] -i 输入文件} ... {[输出文件选项] 输出文件} ...
```

### 1. 基础格式转换
```bash
# MP4转AVI
ffmpeg -i input.mp4 output.avi

# 指定编码器
ffmpeg -i input.mp4 -c:v libx264 -c:a aac output.mp4

# 复制流（不重新编码）
ffmpeg -i input.mp4 -c copy output.mkv
```

### 2. 视频参数调整
```bash
# 调整分辨率
ffmpeg -i input.mp4 -s 1280x720 output.mp4

# 调整帧率
ffmpeg -i input.mp4 -r 30 output.mp4

# 调整比特率
ffmpeg -i input.mp4 -b:v 1M output.mp4

# 调整质量（CRF值，越小质量越高）
ffmpeg -i input.mp4 -crf 23 output.mp4
```

### 3. 音频参数调整
```bash
# 调整音频比特率
ffmpeg -i input.mp4 -b:a 128k output.mp4

# 调整采样率
ffmpeg -i input.mp4 -ar 44100 output.mp4

# 调整声道数
ffmpeg -i input.mp4 -ac 2 output.mp4
```

### 4. 时间相关操作
```bash
# 截取视频片段（从10秒开始，持续30秒）
ffmpeg -ss 10 -t 30 -i input.mp4 output.mp4

# 截取到指定时间点
ffmpeg -ss 10 -to 40 -i input.mp4 output.mp4

# 设置开始时间（输入选项，更精确）
ffmpeg -ss 10 -i input.mp4 -t 30 output.mp4
```

### 5. 流选择和映射
```bash
# 只保留视频流
ffmpeg -i input.mp4 -an output.mp4

# 只保留音频流
ffmpeg -i input.mp4 -vn output.mp3

# 选择特定流
ffmpeg -i input.mp4 -map 0:0 -map 0:2 output.mp4
```

---

## 常用音视频处理场景

### 1. 视频压缩优化
```bash
# 高质量压缩（适合存储）
ffmpeg -i input.mp4 -c:v libx264 -crf 18 -preset slow output.mp4

# 快速压缩（适合实时处理）
ffmpeg -i input.mp4 -c:v libx264 -crf 23 -preset fast output.mp4

# 移动设备优化
ffmpeg -i input.mp4 -c:v libx264 -profile:v baseline -level 3.0 -crf 23 output.mp4
```

### 2. 缩略图生成
```bash
# 生成单张缩略图
ffmpeg -i input.mp4 -ss 00:00:10 -vframes 1 thumbnail.jpg

# 生成多张缩略图
ffmpeg -i input.mp4 -vf fps=1/10 thumb_%03d.jpg

# 指定缩略图尺寸
ffmpeg -i input.mp4 -ss 10 -vframes 1 -s 320x240 thumbnail.jpg
```

### 3. 音频提取和处理
```bash
# 提取音频
ffmpeg -i input.mp4 -vn -c:a copy audio.aac

# 音频格式转换
ffmpeg -i input.wav -c:a libmp3lame -b:a 192k output.mp3

# 音频降噪（需要先生成噪音样本）
ffmpeg -i input.wav -af "highpass=f=200, lowpass=f=3000" output.wav
```

### 4. 视频拼接和分割
```bash
# 创建文件列表
echo "file 'video1.mp4'" > filelist.txt
echo "file 'video2.mp4'" >> filelist.txt

# 拼接视频
ffmpeg -f concat -safe 0 -i filelist.txt -c copy output.mp4

# 按时间分割
ffmpeg -i input.mp4 -t 00:30:00 -c copy part1.mp4 -ss 00:30:00 -c copy part2.mp4
```

### 5. 水印添加
```bash
# 添加图片水印
ffmpeg -i input.mp4 -i watermark.png -filter_complex "overlay=10:10" output.mp4

# 添加文字水印
ffmpeg -i input.mp4 -vf "drawtext=text='Copyright':fontsize=24:fontcolor=white:x=10:y=10" output.mp4
```

### 6. 视频旋转和翻转
```bash
# 顺时针旋转90度
ffmpeg -i input.mp4 -vf "transpose=1" output.mp4

# 水平翻转
ffmpeg -i input.mp4 -vf "hflip" output.mp4

# 垂直翻转
ffmpeg -i input.mp4 -vf "vflip" output.mp4
```

---

## Android集成实践

### 1. 添加FFmpeg依赖
在Android项目中集成FFmpeg有多种方式：

#### 方式一：使用预编译库
```gradle
dependencies {
    implementation 'com.arthenica:mobile-ffmpeg-full:4.4.LTS'
}
```

#### 方式二：使用FFmpegKit（推荐）
```gradle
dependencies {
    implementation 'com.arthenica:ffmpeg-kit-android:4.5.1'
}
```

### 2. 基本使用示例
```java
import com.arthenica.ffmpegkit.FFmpegKit;
import com.arthenica.ffmpegkit.ReturnCode;

public class VideoProcessor {
    
    public void convertVideo(String inputPath, String outputPath) {
        String command = String.format("-i %s -c:v libx264 -crf 23 %s", 
                                     inputPath, outputPath);
        
        FFmpegKit.execute(command, session -> {
            if (ReturnCode.isSuccess(session.getReturnCode())) {
                // 转换成功
                Log.d("FFmpeg", "Video conversion successful");
            } else {
                // 转换失败
                Log.e("FFmpeg", "Video conversion failed");
            }
        });
    }
    
    public void compressVideo(String inputPath, String outputPath) {
        String command = String.format(
            "-i %s -c:v libx264 -crf 28 -preset fast -c:a aac -b:a 128k %s",
            inputPath, outputPath);
            
        FFmpegKit.execute(command);
    }
}
```

### 3. 异步处理和进度监控
```java
public void processVideoWithProgress(String inputPath, String outputPath) {
    String command = String.format("-i %s -c:v libx264 -crf 23 %s", 
                                 inputPath, outputPath);
    
    FFmpegKit.executeAsync(command, session -> {
        if (ReturnCode.isSuccess(session.getReturnCode())) {
            runOnUiThread(() -> {
                // 更新UI - 处理完成
                showSuccess("视频处理完成");
            });
        } else {
            runOnUiThread(() -> {
                // 更新UI - 处理失败
                showError("视频处理失败");
            });
        }
    }, log -> {
        // 处理日志输出
        Log.d("FFmpeg", log.getMessage());
    }, statistics -> {
        // 更新进度
        int progress = (int) (statistics.getTime() * 100 / totalDuration);
        runOnUiThread(() -> updateProgress(progress));
    });
}
```

### 4. 常用Android场景实现

#### 视频压缩
```java
public void compressForAndroid(String input, String output) {
    String command = String.format(
        "-i %s -c:v libx264 -profile:v baseline -level 3.0 " +
        "-crf 23 -maxrate 1M -bufsize 2M -c:a aac -b:a 128k %s",
        input, output);
    FFmpegKit.execute(command);
}
```

#### 生成缩略图
```java
public void generateThumbnail(String videoPath, String thumbnailPath) {
    String command = String.format(
        "-i %s -ss 00:00:01 -vframes 1 -s 320x240 %s",
        videoPath, thumbnailPath);
    FFmpegKit.execute(command);
}
```

#### 音频提取
```java
public void extractAudio(String videoPath, String audioPath) {
    String command = String.format(
        "-i %s -vn -c:a aac -b:a 192k %s",
        videoPath, audioPath);
    FFmpegKit.execute(command);
}
```

---

## 性能优化建议

### 1. 硬件加速
```bash
# 使用Android MediaCodec硬件编码
ffmpeg -i input.mp4 -c:v h264_mediacodec output.mp4

# 使用硬件解码
ffmpeg -hwaccel mediacodec -i input.mp4 -c:v libx264 output.mp4
```

### 2. 预设优化
```bash
# 快速编码（降低质量换取速度）
ffmpeg -i input.mp4 -preset ultrafast output.mp4

# 平衡编码（质量和速度的平衡）
ffmpeg -i input.mp4 -preset medium output.mp4

# 高质量编码（牺牲速度换取质量）
ffmpeg -i input.mp4 -preset slow output.mp4
```

### 3. 多线程处理
```bash
# 指定线程数
ffmpeg -threads 4 -i input.mp4 output.mp4

# 自动检测线程数
ffmpeg -threads 0 -i input.mp4 output.mp4
```

### 4. 内存优化
- 处理大文件时使用流式处理
- 及时释放不需要的资源
- 避免同时处理多个大文件

### 5. Android特定优化
```java
// 在后台线程执行FFmpeg操作
new Thread(() -> {
    FFmpegKit.execute(command);
}).start();

// 使用合适的文件路径
File outputDir = context.getExternalFilesDir(Environment.DIRECTORY_MOVIES);
String outputPath = new File(outputDir, "output.mp4").getAbsolutePath();
```

---

## 常见问题解答

### Q1: FFmpeg命令在Android中执行失败？
**A:** 检查以下几点：
- 文件路径是否正确（使用绝对路径）
- 文件是否存在且有读写权限
- 输出目录是否存在
- 命令语法是否正确

### Q2: 视频处理速度太慢？
**A:** 优化建议：
- 使用硬件加速（MediaCodec）
- 选择合适的预设（preset）
- 降低输出质量参数
- 使用多线程处理

### Q3: 处理后的视频在某些设备上无法播放？
**A:** 兼容性优化：
- 使用baseline profile
- 限制分辨率和比特率
- 选择广泛支持的编码格式

### Q4: 如何获取视频处理进度？
**A:** 使用FFmpegKit的统计回调：
```java
FFmpegKit.executeAsync(command, session -> {
    // 完成回调
}, log -> {
    // 日志回调
}, statistics -> {
    // 进度回调
    int progress = (int)(statistics.getTime() * 100 / totalDuration);
});
```

### Q5: 内存占用过高怎么办？
**A:** 内存优化：
- 避免同时处理多个文件
- 使用合适的缓冲区大小
- 及时释放资源
- 考虑分段处理大文件

---

## 总结

FFmpeg是Android音视频开发的强大工具，掌握其基本概念和常用命令对Android开发者来说非常重要。通过本手册的学习，你应该能够：

1. 理解FFmpeg的核心概念和工作原理
2. 掌握基本的命令行操作
3. 在Android项目中集成和使用FFmpeg
4. 处理常见的音视频处理需求
5. 优化性能和解决常见问题

建议在实际项目中多加练习，结合具体需求深入学习FFmpeg的高级功能。

---

## 高级应用场景

### 1. 直播推流
```java
public class LiveStreaming {
    public void startLiveStream(String inputSource, String rtmpUrl) {
        String command = String.format(
            "-f android_camera -i 0:0 -f pulse -i default " +
            "-c:v libx264 -preset ultrafast -tune zerolatency " +
            "-c:a aac -ar 44100 -f flv %s",
            rtmpUrl);

        FFmpegKit.executeAsync(command, session -> {
            if (ReturnCode.isSuccess(session.getReturnCode())) {
                Log.d("LiveStream", "Stream started successfully");
            }
        });
    }
}
```

### 2. 视频特效处理
```java
public class VideoEffects {

    // 添加模糊效果
    public void addBlurEffect(String input, String output) {
        String command = String.format(
            "-i %s -vf \"boxblur=5:1\" %s", input, output);
        FFmpegKit.execute(command);
    }

    // 调整亮度和对比度
    public void adjustBrightnessContrast(String input, String output,
                                       float brightness, float contrast) {
        String command = String.format(
            "-i %s -vf \"eq=brightness=%.2f:contrast=%.2f\" %s",
            input, brightness, contrast, output);
        FFmpegKit.execute(command);
    }

    // 添加慢动作效果
    public void createSlowMotion(String input, String output, float speed) {
        String command = String.format(
            "-i %s -vf \"setpts=%.2f*PTS\" %s", input, 1/speed, output);
        FFmpegKit.execute(command);
    }
}
```

### 3. 音频处理进阶
```java
public class AudioProcessing {

    // 音频混合
    public void mixAudio(String audio1, String audio2, String output) {
        String command = String.format(
            "-i %s -i %s -filter_complex \"[0:a][1:a]amix=inputs=2[a]\" " +
            "-map \"[a]\" %s", audio1, audio2, output);
        FFmpegKit.execute(command);
    }

    // 音频淡入淡出
    public void addFadeEffect(String input, String output,
                            int fadeInDuration, int fadeOutDuration) {
        String command = String.format(
            "-i %s -af \"afade=t=in:ss=0:d=%d,afade=t=out:st=%d:d=%d\" %s",
            input, fadeInDuration, fadeOutDuration, fadeOutDuration, output);
        FFmpegKit.execute(command);
    }

    // 音频降噪
    public void reduceNoise(String input, String output) {
        String command = String.format(
            "-i %s -af \"highpass=f=200,lowpass=f=3000\" %s", input, output);
        FFmpegKit.execute(command);
    }
}
```

### 4. 批量处理
```java
public class BatchProcessor {

    public void batchCompress(List<String> inputFiles, String outputDir) {
        for (int i = 0; i < inputFiles.size(); i++) {
            String input = inputFiles.get(i);
            String output = outputDir + "/compressed_" + i + ".mp4";

            String command = String.format(
                "-i %s -c:v libx264 -crf 23 -preset fast %s", input, output);

            FFmpegKit.execute(command);
        }
    }

    public void batchGenerateThumbnails(List<String> videoFiles, String thumbDir) {
        for (int i = 0; i < videoFiles.size(); i++) {
            String video = videoFiles.get(i);
            String thumbnail = thumbDir + "/thumb_" + i + ".jpg";

            String command = String.format(
                "-i %s -ss 00:00:01 -vframes 1 -s 320x240 %s", video, thumbnail);

            FFmpegKit.execute(command);
        }
    }
}
```

## 错误处理和调试

### 1. 错误处理最佳实践
```java
public class ErrorHandling {

    public void processVideoWithErrorHandling(String input, String output) {
        String command = String.format("-i %s -c:v libx264 %s", input, output);

        FFmpegKit.execute(command, session -> {
            ReturnCode returnCode = session.getReturnCode();

            if (ReturnCode.isSuccess(returnCode)) {
                Log.d("FFmpeg", "Processing successful");
                onSuccess();
            } else if (ReturnCode.isCancel(returnCode)) {
                Log.w("FFmpeg", "Processing cancelled");
                onCancelled();
            } else {
                Log.e("FFmpeg", "Processing failed with return code: " + returnCode);

                // 获取详细错误信息
                String failStackTrace = session.getFailStackTrace();
                if (failStackTrace != null) {
                    Log.e("FFmpeg", "Error details: " + failStackTrace);
                }

                onError(returnCode.getValue());
            }
        });
    }

    private void onSuccess() {
        // 处理成功逻辑
    }

    private void onCancelled() {
        // 处理取消逻辑
    }

    private void onError(int errorCode) {
        // 处理错误逻辑
        switch (errorCode) {
            case 1:
                showError("输入文件不存在或无法访问");
                break;
            case 2:
                showError("输出路径无效或无写入权限");
                break;
            default:
                showError("处理失败，错误代码：" + errorCode);
        }
    }
}
```

### 2. 日志和调试
```java
public class DebuggingHelper {

    public void enableDetailedLogging() {
        // 设置日志级别
        FFmpegKitConfig.setLogLevel(Level.AV_LOG_DEBUG);

        // 设置日志回调
        FFmpegKitConfig.enableLogCallback(log -> {
            Log.d("FFmpeg", String.format("[%s] %s",
                log.getLevel(), log.getMessage()));
        });
    }

    public void analyzeVideoFile(String filePath) {
        String command = String.format("-i %s", filePath);

        FFmpegKit.execute(command, session -> {
            // 分析输出信息
            List<Log> logs = session.getAllLogs();
            for (Log log : logs) {
                String message = log.getMessage();
                if (message.contains("Video:")) {
                    Log.i("VideoInfo", "Video stream: " + message);
                } else if (message.contains("Audio:")) {
                    Log.i("AudioInfo", "Audio stream: " + message);
                }
            }
        });
    }
}
```

## 性能监控和优化

### 1. 性能监控
```java
public class PerformanceMonitor {

    public void monitorProcessing(String input, String output) {
        long startTime = System.currentTimeMillis();

        String command = String.format("-i %s -c:v libx264 %s", input, output);

        FFmpegKit.execute(command, session -> {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            Log.i("Performance", "Processing time: " + duration + "ms");

            // 获取统计信息
            Statistics statistics = session.getStatistics();
            if (statistics != null) {
                Log.i("Performance", "Video frame number: " + statistics.getVideoFrameNumber());
                Log.i("Performance", "Video fps: " + statistics.getVideoFps());
                Log.i("Performance", "Bitrate: " + statistics.getBitrate());
            }
        });
    }
}
```

### 2. 内存管理
```java
public class MemoryManager {

    public void processWithMemoryControl(String input, String output) {
        // 检查可用内存
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long availableMemory = maxMemory - usedMemory;

        if (availableMemory < 100 * 1024 * 1024) { // 少于100MB
            Log.w("Memory", "Low memory available, using conservative settings");

            // 使用保守的处理参数
            String command = String.format(
                "-i %s -c:v libx264 -preset ultrafast -crf 28 %s",
                input, output);
            FFmpegKit.execute(command);
        } else {
            // 使用标准参数
            String command = String.format(
                "-i %s -c:v libx264 -preset medium -crf 23 %s",
                input, output);
            FFmpegKit.execute(command);
        }
    }

    public void cleanupResources() {
        // 清理FFmpeg会话
        FFmpegKit.cancel();

        // 强制垃圾回收
        System.gc();
    }
}
```

## 实际项目集成指南

### 1. 项目配置
```gradle
android {
    compileSdkVersion 33

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 33

        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a'
        }
    }

    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }
}

dependencies {
    implementation 'com.arthenica:ffmpeg-kit-android:4.5.1'

    // 如果需要完整功能
    // implementation 'com.arthenica:ffmpeg-kit-android-full:4.5.1'
}
```

### 2. 权限配置
```xml
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.INTERNET" />
```

### 3. 文件管理工具类
```java
public class FileUtils {

    public static String getOutputPath(Context context, String fileName) {
        File outputDir = context.getExternalFilesDir(Environment.DIRECTORY_MOVIES);
        if (outputDir != null && !outputDir.exists()) {
            outputDir.mkdirs();
        }
        return new File(outputDir, fileName).getAbsolutePath();
    }

    public static boolean isFileExists(String filePath) {
        File file = new File(filePath);
        return file.exists() && file.isFile();
    }

    public static long getFileSize(String filePath) {
        File file = new File(filePath);
        return file.exists() ? file.length() : 0;
    }

    public static void deleteFile(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            file.delete();
        }
    }
}
```

## FFmpeg命令速查表

### 基础操作
| 功能 | 命令示例 | 说明 |
|------|----------|------|
| 格式转换 | `ffmpeg -i input.mp4 output.avi` | 基础格式转换 |
| 复制流 | `ffmpeg -i input.mp4 -c copy output.mkv` | 不重新编码，快速转换 |
| 提取音频 | `ffmpeg -i input.mp4 -vn output.mp3` | 只保留音频流 |
| 提取视频 | `ffmpeg -i input.mp4 -an output.mp4` | 只保留视频流 |
| 生成缩略图 | `ffmpeg -i input.mp4 -ss 10 -vframes 1 thumb.jpg` | 在第10秒生成缩略图 |

### 视频处理
| 功能 | 命令示例 | 说明 |
|------|----------|------|
| 调整分辨率 | `ffmpeg -i input.mp4 -s 1280x720 output.mp4` | 改变视频分辨率 |
| 调整帧率 | `ffmpeg -i input.mp4 -r 30 output.mp4` | 设置输出帧率为30fps |
| 调整比特率 | `ffmpeg -i input.mp4 -b:v 1M output.mp4` | 设置视频比特率为1Mbps |
| 质量控制 | `ffmpeg -i input.mp4 -crf 23 output.mp4` | CRF质量控制（18-28推荐） |
| 视频截取 | `ffmpeg -ss 10 -t 30 -i input.mp4 output.mp4` | 从10秒开始截取30秒 |

### 音频处理
| 功能 | 命令示例 | 说明 |
|------|----------|------|
| 调整音频比特率 | `ffmpeg -i input.mp4 -b:a 128k output.mp4` | 设置音频比特率 |
| 调整采样率 | `ffmpeg -i input.mp4 -ar 44100 output.mp4` | 设置采样率为44.1kHz |
| 调整声道数 | `ffmpeg -i input.mp4 -ac 2 output.mp4` | 设置为立体声 |
| 音频混合 | `ffmpeg -i audio1.mp3 -i audio2.mp3 -filter_complex amix output.mp3` | 混合两个音频 |

### 滤镜效果
| 功能 | 命令示例 | 说明 |
|------|----------|------|
| 视频旋转 | `ffmpeg -i input.mp4 -vf "transpose=1" output.mp4` | 顺时针旋转90度 |
| 水平翻转 | `ffmpeg -i input.mp4 -vf "hflip" output.mp4` | 水平镜像 |
| 添加水印 | `ffmpeg -i video.mp4 -i logo.png -filter_complex "overlay=10:10" output.mp4` | 添加图片水印 |
| 模糊效果 | `ffmpeg -i input.mp4 -vf "boxblur=5:1" output.mp4` | 添加模糊效果 |
| 亮度调整 | `ffmpeg -i input.mp4 -vf "eq=brightness=0.1" output.mp4` | 增加亮度 |

### Android优化参数
| 场景 | 推荐参数 | 说明 |
|------|----------|------|
| 移动设备兼容 | `-c:v libx264 -profile:v baseline -level 3.0` | 最大兼容性 |
| 快速编码 | `-preset ultrafast -crf 28` | 速度优先 |
| 质量优先 | `-preset slow -crf 18` | 质量优先 |
| 平衡模式 | `-preset medium -crf 23` | 质量和速度平衡 |
| 硬件加速 | `-c:v h264_mediacodec` | 使用Android硬件编码 |

## 故障排除指南

### 常见错误及解决方案

#### 1. "No such file or directory"
**原因**：文件路径错误或文件不存在
**解决方案**：
- 检查文件路径是否正确
- 确保使用绝对路径
- 验证文件是否存在

#### 2. "Permission denied"
**原因**：没有文件读写权限
**解决方案**：
- 检查Android权限声明
- 确保输出目录存在且可写
- 使用应用私有目录

#### 3. "Codec not found"
**原因**：指定的编解码器不支持
**解决方案**：
- 使用 `ffmpeg -codecs` 查看支持的编解码器
- 选择替代的编解码器
- 检查FFmpeg编译配置

#### 4. "Invalid argument"
**原因**：命令参数错误
**解决方案**：
- 检查命令语法
- 验证参数值的有效性
- 参考官方文档

#### 5. 内存不足
**原因**：处理大文件时内存溢出
**解决方案**：
- 降低处理质量参数
- 分段处理大文件
- 使用流式处理

### 调试技巧

#### 1. 启用详细日志
```java
FFmpegKitConfig.setLogLevel(Level.AV_LOG_DEBUG);
FFmpegKitConfig.enableLogCallback(log -> {
    Log.d("FFmpeg", log.getMessage());
});
```

#### 2. 检查文件信息
```bash
ffprobe -v quiet -print_format json -show_format -show_streams input.mp4
```

#### 3. 测试命令
在集成到Android之前，先在命令行测试FFmpeg命令的正确性。

## 最佳实践总结

### 1. 性能优化
- 优先使用硬件加速
- 选择合适的预设参数
- 避免不必要的重新编码
- 合理使用多线程

### 2. 用户体验
- 提供处理进度反馈
- 支持取消操作
- 处理错误情况
- 优化处理时间

### 3. 资源管理
- 及时释放资源
- 监控内存使用
- 避免同时处理多个大文件
- 清理临时文件

### 4. 兼容性
- 使用广泛支持的格式
- 测试不同设备
- 考虑Android版本差异
- 提供降级方案

## 参考资源

- [FFmpeg官方网站](https://ffmpeg.org/)
- [FFmpeg官方文档](https://ffmpeg.org/documentation.html)
- [FFmpegKit Android库](https://github.com/arthenica/ffmpeg-kit)
- [Android MediaCodec文档](https://developer.android.com/reference/android/media/MediaCodec)
- [FFmpeg滤镜文档](https://ffmpeg.org/ffmpeg-filters.html)
- [视频编码最佳实践](https://trac.ffmpeg.org/wiki/Encode/H.264)
- [FFmpeg Wiki](https://trac.ffmpeg.org/wiki)
- [Android音视频开发指南](https://developer.android.com/guide/topics/media)