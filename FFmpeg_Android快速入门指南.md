# FFmpeg Android开发者快速入门指南

## 🚀 快速开始

### 什么是FFmpeg？
FFmpeg是一个强大的跨平台音视频处理框架，支持几乎所有音视频格式的编解码、转换和处理。

### 为什么Android开发者需要FFmpeg？
- **视频应用开发**：短视频、直播、播放器应用
- **音频处理**：音频录制、格式转换、音效处理
- **媒体优化**：压缩、格式转换、质量调整
- **特效处理**：滤镜、水印、剪辑功能

---

## 📱 Android集成

### 1. 添加依赖
```gradle
dependencies {
    implementation 'com.arthenica:ffmpeg-kit-android:4.5.1'
}
```

### 2. 基本使用
```java
import com.arthenica.ffmpegkit.FFmpegKit;

// 简单的视频转换
String command = "-i input.mp4 -c:v libx264 -crf 23 output.mp4";
FFmpegKit.execute(command);

// 带回调的异步处理
FFmpegKit.executeAsync(command, session -> {
    if (ReturnCode.isSuccess(session.getReturnCode())) {
        Log.d("FFmpeg", "转换成功");
    } else {
        Log.e("FFmpeg", "转换失败");
    }
});
```

---

## 🛠️ 常用命令速查

### 基础操作
```bash
# 格式转换
ffmpeg -i input.mp4 output.avi

# 视频压缩（Android优化）
ffmpeg -i input.mp4 -c:v libx264 -profile:v baseline -crf 23 output.mp4

# 提取音频
ffmpeg -i input.mp4 -vn output.mp3

# 生成缩略图
ffmpeg -i input.mp4 -ss 10 -vframes 1 thumbnail.jpg

# 视频截取（从10秒开始，持续30秒）
ffmpeg -ss 10 -t 30 -i input.mp4 output.mp4
```

### 视频处理
```bash
# 调整分辨率
ffmpeg -i input.mp4 -s 1280x720 output.mp4

# 调整比特率
ffmpeg -i input.mp4 -b:v 1M output.mp4

# 添加水印
ffmpeg -i video.mp4 -i logo.png -filter_complex "overlay=10:10" output.mp4

# 视频旋转（顺时针90度）
ffmpeg -i input.mp4 -vf "transpose=1" output.mp4
```

### 音频处理
```bash
# 音频格式转换
ffmpeg -i input.wav -c:a libmp3lame -b:a 192k output.mp3

# 调整音量
ffmpeg -i input.mp4 -af "volume=1.5" output.mp4

# 音频混合
ffmpeg -i audio1.mp3 -i audio2.mp3 -filter_complex amix output.mp3
```

---

## 💡 Android实用示例

### 1. 视频压缩类
```java
public class VideoCompressor {
    
    public static void compressVideo(String inputPath, String outputPath, 
                                   CompressCallback callback) {
        String command = String.format(
            "-i %s -c:v libx264 -profile:v baseline -level 3.0 " +
            "-crf 23 -preset fast -c:a aac -b:a 128k %s",
            inputPath, outputPath);
        
        FFmpegKit.executeAsync(command, session -> {
            if (ReturnCode.isSuccess(session.getReturnCode())) {
                callback.onSuccess(outputPath);
            } else {
                callback.onError("压缩失败");
            }
        });
    }
    
    interface CompressCallback {
        void onSuccess(String outputPath);
        void onError(String error);
    }
}
```

### 2. 缩略图生成器
```java
public class ThumbnailGenerator {
    
    public static void generateThumbnail(String videoPath, String thumbnailPath,
                                       int timeSeconds) {
        String command = String.format(
            "-i %s -ss %d -vframes 1 -s 320x240 %s",
            videoPath, timeSeconds, thumbnailPath);
        
        FFmpegKit.execute(command);
    }
}
```

### 3. 音频提取器
```java
public class AudioExtractor {
    
    public static void extractAudio(String videoPath, String audioPath) {
        String command = String.format(
            "-i %s -vn -c:a aac -b:a 192k %s",
            videoPath, audioPath);
        
        FFmpegKit.execute(command);
    }
}
```

### 4. 进度监控
```java
public class ProgressMonitor {
    
    public void processWithProgress(String input, String output, 
                                  ProgressCallback callback) {
        String command = String.format("-i %s -c:v libx264 %s", input, output);
        
        FFmpegKit.executeAsync(command, 
            session -> {
                // 完成回调
                callback.onComplete(ReturnCode.isSuccess(session.getReturnCode()));
            },
            log -> {
                // 日志回调
                Log.d("FFmpeg", log.getMessage());
            },
            statistics -> {
                // 进度回调
                int progress = calculateProgress(statistics);
                callback.onProgress(progress);
            });
    }
    
    private int calculateProgress(Statistics statistics) {
        // 根据统计信息计算进度百分比
        return (int) (statistics.getTime() * 100 / totalDuration);
    }
    
    interface ProgressCallback {
        void onProgress(int percentage);
        void onComplete(boolean success);
    }
}
```

---

## ⚡ 性能优化技巧

### 1. 硬件加速
```java
// 使用Android MediaCodec硬件编码
String command = "-i input.mp4 -c:v h264_mediacodec output.mp4";
```

### 2. 预设选择
```bash
# 速度优先（质量较低）
-preset ultrafast

# 平衡模式（推荐）
-preset medium

# 质量优先（速度较慢）
-preset slow
```

### 3. 移动设备优化参数
```bash
# 最大兼容性
-c:v libx264 -profile:v baseline -level 3.0

# 快速编码
-preset fast -crf 28

# 小文件大小
-crf 28 -maxrate 1M -bufsize 2M
```

---

## 🔧 常见问题解决

### 问题1：文件路径错误
```java
// ❌ 错误：相对路径
String path = "video.mp4";

// ✅ 正确：绝对路径
String path = context.getExternalFilesDir(Environment.DIRECTORY_MOVIES) 
              + "/video.mp4";
```

### 问题2：权限不足
```xml
<!-- 在AndroidManifest.xml中添加权限 -->
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

### 问题3：内存不足
```java
// 检查可用内存
Runtime runtime = Runtime.getRuntime();
long availableMemory = runtime.maxMemory() - 
                      (runtime.totalMemory() - runtime.freeMemory());

if (availableMemory < 100 * 1024 * 1024) { // 小于100MB
    // 使用保守参数
    command += " -preset ultrafast -crf 28";
}
```

### 问题4：处理大文件
```java
// 分段处理大文件
public void processLargeFile(String input, String output) {
    // 先获取文件时长
    String durationCommand = String.format("-i %s", input);
    // 然后分段处理
    // ...
}
```

---

## 📋 快速参考表

| 需求 | 命令模板 | 说明 |
|------|----------|------|
| 视频压缩 | `-i input.mp4 -crf 23 output.mp4` | 平衡质量和大小 |
| 格式转换 | `-i input.avi -c copy output.mp4` | 快速转换容器 |
| 提取音频 | `-i input.mp4 -vn output.mp3` | 只要音频 |
| 生成缩略图 | `-i input.mp4 -ss 10 -vframes 1 thumb.jpg` | 第10秒截图 |
| 调整分辨率 | `-i input.mp4 -s 720x480 output.mp4` | 改变尺寸 |
| 添加水印 | `-i video.mp4 -i logo.png -filter_complex overlay output.mp4` | 图片水印 |

---

## 🎯 最佳实践

1. **始终使用绝对路径**
2. **在后台线程执行FFmpeg操作**
3. **提供用户进度反馈**
4. **处理错误情况**
5. **优化移动设备参数**
6. **及时清理临时文件**
7. **测试不同设备兼容性**

---

## 📚 进阶学习

- [完整学习手册](./FFmpeg_Android开发者学习手册.md)
- [FFmpeg官方文档](https://ffmpeg.org/documentation.html)
- [FFmpegKit GitHub](https://github.com/arthenica/ffmpeg-kit)

---

**开始你的FFmpeg Android开发之旅吧！** 🚀
